/* Bundle Builder - Clean Layout */

/* Container */
.bundle-builder-container {
  min-height: 100vh;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
}

/* Header */
.builder-header {
  background: white;
  border-bottom: 1px solid #dee2e6;
  padding: 20px 0;
  flex-shrink: 0;
}

.builder-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #495057;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 10px;
}

/* Main Content */
.builder-main {
  display: flex;
  flex: 1;
  gap: 20px;
  padding: 20px;
  padding-bottom: 120px; /* Extra space for any floating buttons */
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

/* Panels */
.builder-panel {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  flex-direction: column;
  /* Use flexible height that works in modal */
  max-height: calc(100vh - 300px);
  min-height: 400px;
}

/* Panel Header */
.panel-header {
  background: #f8f9fa;
  padding: 20px;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.panel-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #495057;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Panel Content */
.panel-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

/* Categories */
.categories-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  margin-bottom: 20px;
}

.category-card {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.category-card:hover {
  border-color: #007bff;
  background: #e3f2fd;
  transform: translateY(-1px);
}

/* Products */
.products-grid {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.product-card {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 12px;
  cursor: grab;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  width: 100%;
}

.product-image {
  width: 48px;
  height: 48px;
  border-radius: 6px;
  overflow: hidden;
  margin-right: 12px;
  flex-shrink: 0;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-info {
  flex: 1;
  text-align: left;
}

.product-name {
  font-weight: 500;
  color: #212529;
  margin-bottom: 4px;
}

.product-price {
  font-size: 14px;
  color: #28a745;
  font-weight: 600;
}

.product-card:hover {
  border-color: #007bff;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  transform: translateY(-1px);
}

.product-card:active {
  cursor: grabbing;
}

.product-card.dragging {
  opacity: 0.6;
  transform: rotate(2deg) scale(0.95);
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* Bundle Drop Zone */
.bundle-drop-zone {
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  padding: 40px 20px;
  text-align: center;
  background: #f8f9fa;
  transition: all 0.2s ease;
  min-height: 300px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.bundle-drop-zone.drag-over {
  border-color: #28a745;
  background: #d4edda;
}

.drop-zone-empty {
  color: #6c757d;
}

.drop-zone-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.drop-zone-text {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
}

.drop-zone-hint {
  font-size: 14px;
  opacity: 0.7;
}

/* Selected Products */
.selected-products-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.selected-product-card {
  background: white;
  border: 1px solid #007bff;
  border-radius: 8px;
  padding: 12px;
  text-align: center;
  position: relative;
}

.remove-product-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Action Bar */
.builder-actions {
  background: white;
  border-top: 1px solid #dee2e6;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.bundle-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.product-count-badge {
  background: #e9ecef;
  color: #495057;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

/* Modal-specific styles */
.modal-body .bundle-builder-container {
  height: 100% !important;
  min-height: auto !important;
}

.modal-body .builder-main {
  height: calc(100% - 140px) !important; /* Account for header and action bar */
  padding-bottom: 20px !important; /* Reduce bottom padding in modal */
}

.modal-body .builder-panel {
  max-height: none !important;
  height: 100% !important;
  min-height: auto !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .builder-main {
    flex-direction: column;
    padding: 10px;
    padding-bottom: 20px;
  }

  .builder-panel {
    height: auto;
    flex: none;
    max-height: 300px;
    min-height: 250px;
    margin-bottom: 10px;
  }

  .categories-grid {
    grid-template-columns: 1fr;
  }

  .selected-products-grid {
    grid-template-columns: 1fr;
  }

  .builder-actions {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
    height: auto;
    padding: 15px;
  }

  .action-buttons {
    justify-content: center;
  }

  /* Modal mobile adjustments */
  .modal-body .builder-main {
    height: auto !important;
    flex-direction: column;
  }

  .modal-body .builder-panel {
    height: 300px !important;
    max-height: 300px !important;
  }
}

